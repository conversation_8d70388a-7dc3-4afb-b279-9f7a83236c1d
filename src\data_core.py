import numpy as np
import warnings
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, Iterator, List, Optional, Set, Union

@dataclass(frozen=True, repr=False)
class DataInstance:
    """
    An immutable dataclass holding data for a single instance.

    Uses @dataclass(frozen=True) for immutability and provides both
    attribute-style (`instance.param_a`) and dictionary-style
    (`instance['param_a']`) read access.
    """
    _data: Dict[str, Any] = field(init=False, repr=False)

    def __init__(self, **kwargs: Any):
        """Initializes the instance with key-value data."""
        object.__setattr__(self, '_data', kwargs)

    def __getattr__(self, name: str) -> Any:
        """Enables attribute-style read access (e.g., `instance.key`)."""
        try:
            return self._data[name]
        except KeyError:
            raise AttributeError(f"'DataInstance' object has no attribute '{name}'")

    def __getitem__(self, key: str) -> Any:
        """Enables dictionary-style read access (e.g., `instance['key']`)."""
        return self._data[key]

    def __repr__(self) -> str:
        """Provides an unambiguous string representation."""
        newline = "\n"
        content_str = ', '.join(f"{k.replace(newline, '')}={f'{v:.2f}' if isinstance(v, float) else repr(v)}" for k, v in self._data.items())
        return f"DataInstance({content_str})"

    def __len__(self) -> int:
        """Returns the number of parameters."""
        return len(self._data)

    def __iter__(self) -> Iterator[str]:
        """Allows iteration over parameter keys."""
        return iter(self._data)
    
    def __eq__(self, other: object) -> bool:
        """Checks for equality based on the internal data dictionary."""
        if not isinstance(other, DataInstance):
            return NotImplemented
        return self._data == other._data

    def __hash__(self) -> int:
        """
        Computes a hash based on the contents of the instance.

        To ensure the hash is consistent and order-independent, this
        method creates a frozenset of the (key, value) items.
        """
        return hash(frozenset(self._data.items()))
    
    def get(self, key: str) -> Any:
        """Gets a parameter value."""
        return self._data[key]
    
    def with_updates(self, **kwargs: Any) -> 'DataInstance':
        """Creates a new instance with updated parameters."""
        new_data = self._data.copy()  
        new_data.update(kwargs)
        return DataInstance(**new_data)
    
    def _conform(self, target_params: Set[str], _warn: bool = True) -> 'DataInstance':
        """
        Returns a new instance that conforms to the target parameters.
        
        Fills missing values with NaN. Assumes target_params contain all keys in self, raising a ValueError if they aren't.
        """
        instance_keys = set(self._data.keys())
        extra_keys = instance_keys - target_params
        if extra_keys:
            raise ValueError(f"Instance has extra keys not in target: {sorted(extra_keys)}")

        if instance_keys == target_params:
            return self 

        missing_keys = target_params - instance_keys
        if _warn and missing_keys:
            warnings.warn(f"Instance {self} missing keys: {sorted(missing_keys)}. Filling with NaN.")
        
        updates = {key: np.nan for key in missing_keys}
        return self.with_updates(**updates)
        
    def extract(self, *keys: str) -> List[Any]:
        """Extracts values for multiple keys."""
        return [self._data[key] for key in keys]
    
    def keys(self) -> Set[str]:
        """Returns the parameter keys as a set."""
        return set(self._data.keys())

    def values(self):
        """Returns the parameter values."""
        return self._data.values()

    def items(self):
        """Returns the parameter key-value pairs."""
        return self._data.items()

class Dataset:
    """A container for a collection of DataInstance objects."""

    def __init__(self, instances: Optional[List[DataInstance]] = None, fix_integrity: bool = False):
        """
        Initializes the Dataset.

        Args:
            instances: An optional list of DataInstance objects.
            fix_integrity: If True, automatically conforms all instances to a
                unified set of parameters by filling missing values with NaN.
                If False, raises a ValueError if instances have mismatched parameters.
        """
        self._fix_integrity = fix_integrity
        self._instances: List[DataInstance] = []
        self._parameters: Set[str] = set()

        if instances:  # Use extend logic which handles verification and conforming
            self.extend(instances)

    def _verify_integrity(self):
        """
        Ensures all instances have the same set of parameters.

        If `_fix_integrity` is False, it verifies that all instances share the
        exact same set of parameters, raising a ValueError on mismatch.
        If `_fix_integrity` is True, it conforms all instances to the union of
        all parameters found in the dataset.
        """
        if not self._instances:
            return

        if self._fix_integrity:
            # Conform all existing instances to the global parameter set
            self._instances = [inst._conform(self._parameters, _warn=True) for inst in self._instances]
        else:
            # Strict check: all instances must have the same keys as the global set
            for i, inst in enumerate(self._instances):
                if inst.keys() != self._parameters:
                    raise ValueError(
                        f"Dataset integrity error with fix_integrity=False. "
                        f"Instance {i} has keys {inst.keys()} but expected {self._parameters}."
                    )

    @property
    def instances(self) -> List[DataInstance]:
        """Gets a copy of the list of DataInstance objects."""
        return self._instances.copy()  

    @property
    def parameters(self) -> Set[str]:
        """Gets a copy of the set of parameter names."""
        return self._parameters.copy() 

    def __add__(self, other: 'Dataset') -> 'Dataset':
        """
        Concatenates two datasets.

        The resulting dataset's `fix_integrity` flag will be True if either
        of the source datasets has it set to True.
        """
        if not isinstance(other, Dataset):
            return NotImplemented
        
        combined_instances = self._instances + other._instances
        # If either dataset is flexible, the result should be flexible
        new_fix_integrity = self._fix_integrity or other._fix_integrity
        
        # Let the constructor handle the complex task of merging and verifying
        return Dataset(combined_instances, fix_integrity=new_fix_integrity)
    
    def append(self, instance: DataInstance):
        """Appends a single instance, verifying and updating parameters as needed."""
        self.extend([instance])

    def extend(self, instances: List[DataInstance]):
        """
        Extends the dataset with a list of instances, updating parameters efficiently.

        This method contains the core logic for maintaining data integrity.

        Note: If `fix_integrity` is True and new instances introduce parameters
        not already present in the dataset, all existing instances will be
        re-conformed to include the new parameters. This can be an expensive
        operation for very large datasets.
        """
        if not instances:
            return
            
        if self._fix_integrity:
            # Discover new parameters from the incoming instances
            new_params = set().union(*(inst.keys() for inst in instances))
            combined_params = self._parameters.union(new_params)

            if combined_params != self._parameters:
                # If the parameter set grows, we must re-conform existing instances
                self._parameters = combined_params
                self._instances = [inst._conform(self._parameters, _warn=False) for inst in self._instances]

            # Conform and append the new instances
            conformed_new = [inst._conform(self._parameters) for inst in instances]
            self._instances.extend(conformed_new)

        else: # Strict integrity
            # In strict mode, the parameters of new instances must match exactly
            # what the dataset already knows.
            if not self._instances:
                # If dataset is empty, the first batch of instances sets the standard
                self._parameters = instances[0].keys()
            
            for inst in instances:
                if inst.keys() != self._parameters:
                    raise ValueError(
                        f"Cannot add instance with parameters {inst.keys()} to a strict "
                        f"dataset with parameters {self._parameters}."
                    )
            self._instances.extend(instances)

    def add(self, data: Union[DataInstance, List[DataInstance]]):
        """Adds a single instance or a list of instances."""
        if isinstance(data, DataInstance):
            self.append(data)
        elif isinstance(data, list):
            self.extend(data)
        else:
            raise TypeError(f"Expected DataInstance or list of DataInstance, got {type(data)}")

    def __str__(self) -> str:
        """Provides a summary of the dataset."""
        num_instances = len(self._instances)
        num_params = len(self._parameters)
        param_str = f"{num_params} parameters"
        instance_str = f"{num_instances} instances"

        header = f"<Dataset: {instance_str}, {param_str}>"
        if num_instances == 0:
            return header

        examples = "\nExamples:\n"
        # Display up to 5 examples
        for i, instance in enumerate(self._instances[:5]):
            examples += f"  [{i}] {instance}\n"

        params = "\nParameters:\n" 
        # Display up to 10 unique values per parameter
        for i, param in enumerate(sorted(self._parameters)):
            params_values = list(set(self.get_param_list(param)))
            params_key = param.replace("\n", " ")
            params_str1 = ', '.join(f'{v:.2f}' if isinstance(v, float) else repr(v) for v in params_values[:10])
            params_str2 = "..." if len(params_values) > 10 else ""
            params += f"  [{i}] \"{params_key}\" includes: {params_str1}{params_str2}\n"
        return header + examples + params
    
    def __repr__(self) -> str:
        """Provides a clean string representation."""
        return str(self)
    
    def apply(self, func: Callable[[DataInstance], DataInstance]) -> 'Dataset':
        """Applies a function to each instance and returns a new dataset."""
        new_instances = [func(instance) for instance in self._instances]
        return Dataset(new_instances, fix_integrity=self._fix_integrity)
    
    def get_param_list(self, key: str) -> List[Any]:
        """Gets all values for a single parameter, returns a list."""
        if key not in self._parameters:
            raise KeyError(f"Parameter '{key}' not found in dataset.")
        return [instance[key] for instance in self._instances]

    def get_params_array(self, *keys: str) -> np.ndarray:
        """Gets data for multiple parameters as a NumPy array."""
        for key in keys:
            if key not in self._parameters:
                raise KeyError(f"Parameter '{key}' not found in dataset.")

        data_rows = [instance.extract(*keys) for instance in self._instances]
        return np.array(data_rows, dtype=object) # Use dtype=object for mixed types


# --- DEMONSTRATION ---
if __name__ == "__main__":
    print("--- 1. Creating individual DataInstances ---")
    d1 = DataInstance(param_a=1, param_b=10.5, category='A')
    d2 = DataInstance(param_a=2, param_b=12.1, category='A')
    # This instance is missing 'param_b' and has an extra 'param_c'
    d3 = DataInstance(param_a=3, category='B', param_c=100)
    print(d1)
    print(d2)
    print(d3)

    print("\n--- 2. Creating a Dataset (with integrity fixing enabled) ---")
    # This will conform d1 and d2 by adding param_c=NaN, and d3 by adding param_b=NaN
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        dataset = Dataset([d1, d2, d3])
        print("Caught expected warnings during conforming:")
        for warn in w:
            print(f"- {warn.message}")
            
    print(dataset)
    print(f"All parameters in dataset: {sorted(list(dataset.parameters))}")

    print("\n--- 3. Adding new data ---")
    d4 = DataInstance(param_a=4, param_b=15.0, category='B', param_c=101)
    dataset.add(d4) # This instance matches the schema, no conforming needed
    
    # This instance introduces a new parameter 'param_d'
    d_new_param = DataInstance(param_d=999)
    print("Adding an instance with a new parameter 'param_d'...")
    dataset.add(d_new_param) # This will trigger re-conforming of all 5 existing instances
    
    print(dataset)
    print(f"Updated parameters in dataset: {sorted(list(dataset.parameters))}")


    print("\n--- 4. Retrieving data ---")
    categories = dataset.get_param_list('category')
    print(f"Categories: {categories}")
    numerical_data = dataset.get_params_array('param_a', 'param_b')
    print("Numerical data (param_a, param_b):\n", numerical_data)
    assert np.isnan(numerical_data[2, 1])
    print("\nAssertion successful: Missing value was correctly filled with NaN.")

    print("\n--- 5. Testing integrity verification (_fix_integrity=False) ---")
    d5 = DataInstance(param_a=5, param_b=20.0)
    d6 = DataInstance(param_a=6) # Missing 'param_b'

    try:
        # This should fail because d6 is missing a parameter and fixing is disabled.
        strict_dataset = Dataset([d5, d6], fix_integrity=False)
    except ValueError as e:
        print("Successfully caught expected error on initialization:")
        print(e)

    # Now test the .add() method
    strict_dataset = Dataset([d5], fix_integrity=False)
    print("\nCreated a strict dataset with one valid instance:")
    print(strict_dataset)
    
    d7_new_param = DataInstance(param_a=7, param_b=21.0, param_d='new')
    try:
        # This should fail because d7 introduces a new parameter
        strict_dataset.add(d7_new_param)
    except ValueError as e:
        print("\nSuccessfully caught expected error when adding new data with different parameters:")
        print(e)

    try:
        # This should fail because d6 is missing a parameter
        strict_dataset.add(d6)
    except ValueError as e:
        print("\nSuccessfully caught expected error when adding new data with missing parameters:")
        print(e)
from src.data_loader import load_from_xlsx
from src.data_cleaner import joe_data_cleaner


if __name__ == "__main__":
    for file_name in ("All SAT", "All PSE", "All ACC", "PSEcntl"):
        filepath = rf"C:\Users\<USER>\PycharmProjects\Frankenstein\data\DataFromJoe\{file_name}.xlsx"
        dataset = load_from_xlsx(filepath)
        cleaned_dataset = dataset.apply(joe_data_cleaner)
        print(cleaned_dataset)
import warnings
import numpy as np
from typing import Any, Callable, <PERSON>ple

from src.data_core import DataInstance




def default_cleaner(instance: DataInstance) -> DataInstance:
    """A default cleaning function that does nothing."""
    return instance


def typo_template(key: str, correct_value: Any, possible_typos: Tuple[Any, ...]) -> Callable[[DataInstance], DataInstance]:
    """A template for creating a function to fix typos in a specific key."""
    def rule(instance: DataInstance) -> DataInstance:
        if instance[key] in possible_typos:
            warnings.warn(f"Corrected typo in '{key}' from {instance[key]} to {correct_value}.")
            return instance.with_updates(**{key: correct_value})
        return instance
    return rule


def joe_data_cleaner(instance: DataInstance) -> DataInstance:
    """A custom cleaning function for Joe's data."""

    def rule1_unify_nan_value_indicator(instance: DataInstance) -> DataInstance:
        """Unify the NaN value indicator to np.nan."""        
        NAN_KEYWORDS = (0, "0", "?", "Unknown", "Pyramidal?", "No Fill")
        new_data = {}
        for k, v in instance.items():
            if v in NAN_KEYWORDS:
                new_data[k] = np.nan
                warnings.warn(f"Replaced {v} with np.nan for key {k}")
            else:
                new_data[k] = v
        return DataInstance(**new_data)
    
    
    funcs = [
        rule1_unify_nan_value_indicator, 
        typo_template('Morphology', 'Pyramidal', ("Pryamidal", )), 
        typo_template('GrpTrain', 'PSEcntl', ("PSECntl", )),
        typo_template('Training', 'PSEcntl24 + PSE24', ("PCntl24 + PSE24", "PSECntl24 + PSE24",)),
        typo_template('Training', 'PSEcntl48 + PSE24', ("PCntl48 + PSE24", )),
        typo_template('Training', 'PSEcntl72 + PSE24', ("PCntl72 + PSE24", )),
        typo_template('Training', 'ACC24 + SAT24', ("ACC24 +SAT24", )),
    ]
    for func in funcs:
        instance = func(instance)
    return instance